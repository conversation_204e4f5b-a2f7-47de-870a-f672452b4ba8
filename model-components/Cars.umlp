/* (c) https://github.com/MontiCore/monticore */

import java.time.LocalDateTime;

/**
 * This is a model-component for an application representing a car rental.
 *
 * Here we focus on car-related information.
 */
umlp Cars {

  /**
   * Declare the RentableItem class, although it is detailed in Renting.umlp,
   * to maintain a valid Cars class diagram.
   */
  abstract class RentableAsset;

  class Car extends RentableAsset {
    String lineup;
    String manufacturer;
    int mileage = 0;
  }

}
