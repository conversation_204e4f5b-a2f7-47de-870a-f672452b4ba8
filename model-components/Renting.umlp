/* (c) https://github.com/MontiCore/monticore */

import java.time.LocalDateTime;

// One could also use "classdiagram Renting {", but the umlp tool prefers the
// first syntax
umlp Renting {

  // TODO exercise 2.1 a)

  abstract class RentableAsset {
    Optional<String> description;
    RentableStatus status = RentableStatus.AVAILABLE;
  }

  class Pricing {
    double dailyFee;
    double discount = 0.0;
  }

  association [1] RentableAsset -> Pricing [1];

  enum RentableStatus {
    AVAILABLE, RENTED;
  }

  class RentalTransaction {
    // We can use Java types, such as LocalDateTime that we have imported above
    LocalDateTime rentalDate;
    Optional<LocalDateTime> returnDate;
    derived double cost;
  }

  association [*] RentalTransaction <-> (asset) RentableAsset [1];

  class Customer;

  association [*] RentalTransaction <-> (customer) Customer [1];

  class RentalService {
    RentalTransaction rentAsset(RentableAsset asset, Customer customer);
    void returnAsset(RentalTransaction rentalTransaction);
  }

  association [1] RentalService (rental) -> (asset) RentableAsset [*];
  association [1] RentalService (rental) -> (customer) Customer [*];

}
