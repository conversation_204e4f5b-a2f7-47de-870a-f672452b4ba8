package gui;

import carrental.RentableStatus;
import mc.fenix.input.gemdropdowninputtypes.Option;
import umlp.jsweet.extension.annotation.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class CarSave extends CarSaveTOP {

  List<Option> rentableOptions = new ArrayList<>();

  public void initRentableOptions() {
    for (RentableStatus rS: RentableStatus.values()) {
      String val = rS.name();
      rentableOptions.add(new Option(val));
    }
  }

  @Override
  public void init() {
    super.init();
    this.initRentableOptions();
  }

  public List<Option> getRentableOptions() {
    return this.rentableOptions;
  }
}
