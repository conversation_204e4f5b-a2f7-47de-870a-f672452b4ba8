package gui;

import carrental.Car;
import carrental.RentableStatus;
import service.ImageCacheService;
import umlp.guiutil.Router;
import umlp.jsweet.extension.annotation.Component;

import java.util.function.Consumer;

@Component(
    tsImports = {"ImageCacheService, service/ImageCacheService"}
)
public class CarOverview extends CarOverviewTOP {

  ImageCacheService imageCacheService;

  public CarOverview(ImageCacheService imageCacheService) {
    this.imageCacheService = imageCacheService;
  }

  @Override
  public String getAvailableCarsTitle() {
    if (this.getCars() == null) {
      return "";
    }
    int i = 0;
    for (Car c : this.getCars()) {
      if (c.getStatus() == RentableStatus.AVAILABLE) {
        i++;
      }
    }
    return i + " available cars";
  }

  @Override
  public Consumer<Void> updateCar(Car car) {
    return $ -> {
      Router.navigate("gui", "CarUpdate", String.valueOf(car.getGemId()));
    };
  }

  @Override
  public String getImageUrl(String maker) {
    String img = this.imageCacheService.getImageFromStorage(maker);
    if(img != null) {
      return img;
    }
    return "assets/images/" + maker + ".jpg";
  }
}
