package gui;

import carrental.Car;
import carrental.CarRentalManager;
import umlp.guiutil.Router;
import umlp.jsweet.extension.annotation.Component;

import java.util.Optional;
import java.util.function.Consumer;

@Component
public class CarCreate extends CarCreateTOP {

  @Override
  public void initCar() {
    super.initCar();
    this.getCar().setPricing(CarRentalManager.pricingBuilder());
  }

  @Override
  public Consumer<Void> createAndNavigate(Car car) {
    return $ -> {
      Optional<Car> carOptional = (Optional<Car>) this.getCar().build();
      if(carOptional.isPresent()) {
        Router.navigate("gui", "CarOverview");
      }
    };
  }

  @Override
  public Consumer<String> updateMaker(Car car) {
    return maker -> {
      car.setManufacturer(maker);
    };
  }
}
