pluginManagement {
  repositories {
    if(("true").equals(getProperty('useLocalRepo'))) {
      println "Using local repo for plugins"
      mavenLocal()
    }
    maven { // For UMLP Plugin
      credentials.username mavenUser
      credentials.password mavenPassword
      url = repo
    }

    // native image
    maven { url "https://repo.spring.io/snapshot" }
    maven { url "https://repo.spring.io/milestone" }

    gradlePluginPortal()
    mavenCentral()
  }
}

include ":backend"
include ":frontend"

// TODO: Add path to root directory of your local cd2gui project
includeBuild("D:\\RWTH\\SLE\\cd2gui")
