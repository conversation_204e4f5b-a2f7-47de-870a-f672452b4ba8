package cd2gui.ftl;

import cd2gui.data.*;
import de.monticore.cdbasis._ast.ASTCDClass;
import de.se_rwth.commons.logging.Log;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Integrates unused data classes into FTL template generation.
 * Utilizes MetricScale, CD2GUIAssociation, CD2GUIClassTreeNode, and ClassMetrics
 * to provide richer context for template generation.
 * 
 * <AUTHOR> to utilize unused data classes
 */
public class FTLMetricsIntegrator {
    
    /**
     * Integrates ClassMetrics data into FTL template generation context.
     * This utilizes the ClassMetrics class that was previously unused in FTL generation.
     * 
     * @param metricsMap Map of class to AttributeMetric list
     * @return Map of class to ClassMetrics with integrated data
     */
    public Map<ASTCDClass, ClassMetrics> integrateClassMetrics(Map<ASTCDClass, List<AttributeMetric<?>>> metricsMap) {
        Map<ASTCDClass, ClassMetrics> classMetricsMap = new HashMap<>();
        
        if (metricsMap == null || metricsMap.isEmpty()) {
            Log.warn("No metrics map provided for ClassMetrics integration");
            return classMetricsMap;
        }
        
        for (Map.Entry<ASTCDClass, List<AttributeMetric<?>>> entry : metricsMap.entrySet()) {
            ASTCDClass clazz = entry.getKey();
            List<AttributeMetric<?>> metrics = entry.getValue();
            
            if (metrics != null && !metrics.isEmpty()) {
                ClassMetrics classMetrics = new ClassMetrics(metrics);
                classMetricsMap.put(clazz, classMetrics);
                
                Log.info("Integrated ClassMetrics for " + clazz.getName() + 
                        " with " + classMetrics.getTotalAttributesInt() + " attributes", "FTLMetricsIntegrator");
            }
        }
        
        return classMetricsMap;
    }
    
    /**
     * Analyzes MetricScale distribution across attributes for enhanced template generation.
     * This utilizes the MetricScale enum that was previously unused in FTL generation.
     * 
     * @param metrics List of AttributeMetric objects
     * @return MetricScaleAnalysis containing scale distribution data
     */
    public MetricScaleAnalysis analyzeMetricScales(List<AttributeMetric<?>> metrics) {
        MetricScaleAnalysis analysis = new MetricScaleAnalysis();
        
        if (metrics == null || metrics.isEmpty()) {
            return analysis;
        }
        
        // Count scale types (this would require extending AttributeMetric to include MetricScale)
        // For now, we infer scales from chart types
        for (AttributeMetric<?> metric : metrics) {
            ChartType chartType = metric.getType();
            MetricScale inferredScale = inferScaleFromChartType(chartType);
            analysis.addScale(inferredScale);
        }
        
        Log.info("Analyzed metric scales: " + analysis.toString(), "FTLMetricsIntegrator");
        return analysis;
    }
    
    /**
     * Infers MetricScale from ChartType based on the mapping defined in MetricScale documentation.
     * This creates a bridge between the unused MetricScale enum and the current chart generation.
     */
    private MetricScale inferScaleFromChartType(ChartType chartType) {
        switch (chartType) {
            case PIE_CHART:
                return MetricScale.NOMINAL;
            case BAR_CHART:
                return MetricScale.ORDINAL;
            case LINE_CHART:
                return MetricScale.INTERVAL;
            case SCATTER_PLOT:
                return MetricScale.RATIO;
            default:
                return MetricScale.NONE;
        }
    }
    
    /**
     * Generates enhanced template context using CD2GUIClassTreeNode data.
     * This utilizes the class hierarchy information for richer template generation.
     * 
     * @param clazz The class being processed
     * @param classTreeNodes List of class tree nodes
     * @return Enhanced template context with hierarchy information
     */
    public ClassHierarchyContext generateHierarchyContext(ASTCDClass clazz, List<CD2GUIClassTreeNode> classTreeNodes) {
        ClassHierarchyContext context = new ClassHierarchyContext();
        
        if (classTreeNodes == null || classTreeNodes.isEmpty()) {
            return context;
        }
        
        // Find the tree node for the current class
        CD2GUIClassTreeNode currentNode = findTreeNode(clazz, classTreeNodes);
        if (currentNode != null) {
            context.setCurrentClass(clazz.getName());
            context.setHasSubclasses(currentNode.hasSubclasses());
            context.setSubclassCount(currentNode.getSubclasses().size());
            
            // Extract subclass names for template generation
            List<String> subclassNames = new ArrayList<>();
            for (CD2GUIClassTreeNode subNode : currentNode.getSubclasses()) {
                subclassNames.add(subNode.getClazz().getName());
            }
            context.setSubclassNames(subclassNames);
            
            // Generate traversal information
            context.setPreOrderTraversal(currentNode.preOrderTraversal());
            context.setPostOrderTraversal(currentNode.postOrderTraversal());
            
            Log.info("Generated hierarchy context for " + clazz.getName() + 
                    " with " + context.getSubclassCount() + " subclasses", "FTLMetricsIntegrator");
        }
        
        return context;
    }
    
    /**
     * Finds the CD2GUIClassTreeNode for a given class.
     */
    private CD2GUIClassTreeNode findTreeNode(ASTCDClass clazz, List<CD2GUIClassTreeNode> classTreeNodes) {
        for (CD2GUIClassTreeNode node : classTreeNodes) {
            if (node.getClazz().equals(clazz)) {
                return node;
            }
            // Search in subclasses recursively
            CD2GUIClassTreeNode found = findTreeNodeRecursive(clazz, node.getSubclasses());
            if (found != null) {
                return found;
            }
        }
        return null;
    }
    
    /**
     * Recursively searches for a class tree node.
     */
    private CD2GUIClassTreeNode findTreeNodeRecursive(ASTCDClass clazz, List<CD2GUIClassTreeNode> nodes) {
        for (CD2GUIClassTreeNode node : nodes) {
            if (node.getClazz().equals(clazz)) {
                return node;
            }
            CD2GUIClassTreeNode found = findTreeNodeRecursive(clazz, node.getSubclasses());
            if (found != null) {
                return found;
            }
        }
        return null;
    }
    
    /**
     * Container class for metric scale analysis results.
     */
    public static class MetricScaleAnalysis {
        private final Map<MetricScale, Integer> scaleDistribution = new HashMap<>();
        private int totalMetrics = 0;
        
        public void addScale(MetricScale scale) {
            scaleDistribution.merge(scale, 1, Integer::sum);
            totalMetrics++;
        }
        
        public Map<MetricScale, Integer> getScaleDistribution() {
            return new HashMap<>(scaleDistribution);
        }
        
        public int getTotalMetrics() {
            return totalMetrics;
        }
        
        public boolean hasScale(MetricScale scale) {
            return scaleDistribution.containsKey(scale);
        }
        
        public int getScaleCount(MetricScale scale) {
            return scaleDistribution.getOrDefault(scale, 0);
        }
        
        @Override
        public String toString() {
            return "MetricScaleAnalysis{" +
                    "scaleDistribution=" + scaleDistribution +
                    ", totalMetrics=" + totalMetrics +
                    '}';
        }
    }
    
    /**
     * Container class for class hierarchy context information.
     */
    public static class ClassHierarchyContext {
        private String currentClass;
        private boolean hasSubclasses = false;
        private int subclassCount = 0;
        private List<String> subclassNames = new ArrayList<>();
        private String preOrderTraversal = "";
        private String postOrderTraversal = "";
        
        // Getters and setters
        public String getCurrentClass() { return currentClass; }
        public void setCurrentClass(String currentClass) { this.currentClass = currentClass; }
        
        public boolean hasSubclasses() { return hasSubclasses; }
        public void setHasSubclasses(boolean hasSubclasses) { this.hasSubclasses = hasSubclasses; }
        
        public int getSubclassCount() { return subclassCount; }
        public void setSubclassCount(int subclassCount) { this.subclassCount = subclassCount; }
        
        public List<String> getSubclassNames() { return subclassNames; }
        public void setSubclassNames(List<String> subclassNames) { this.subclassNames = subclassNames; }
        
        public String getPreOrderTraversal() { return preOrderTraversal; }
        public void setPreOrderTraversal(String preOrderTraversal) { this.preOrderTraversal = preOrderTraversal; }
        
        public String getPostOrderTraversal() { return postOrderTraversal; }
        public void setPostOrderTraversal(String postOrderTraversal) { this.postOrderTraversal = postOrderTraversal; }
    }
}
