package cd2gui.ftl;

import cd2gui.data.*;
import de.se_rwth.commons.logging.Log;

import java.util.ArrayList;
import java.util.List;

/**
 * Extracts rich data from ChartDetail implementations and other unused data classes.
 * Utilizes the actual chart data, configuration, and metadata instead of generating hardcoded templates.
 * 
 * <AUTHOR> to utilize unused data classes
 */
public class FTLDataExtractor {
    
    /**
     * Extracts comprehensive chart data information from ChartDetail implementations.
     * This method utilizes the rich data structures that were previously unused.
     * 
     * @param detail ChartDetail implementation containing actual chart data
     * @param chartType Type of chart for context
     * @return ChartDataInfo containing extracted metadata and configuration
     */
    public ChartDataInfo extractChartData(ChartDetail<?> detail, ChartType chartType) {
        if (detail == null) {
            Log.warn("Cannot extract data from null ChartDetail");
            return new ChartDataInfo();
        }
        
        ChartDataInfo info = new ChartDataInfo();
        
        try {
            // Extract data based on specific ChartDetail implementation
            if (detail instanceof PieChartDetail) {
                extractPieChartData((PieChartDetail) detail, info);
            } else if (detail instanceof BarChartDetail) {
                extractBarChartData((BarChartDetail) detail, info);
            } else if (detail instanceof LineChartDetail) {
                extractLineChartData((LineChartDetail) detail, info);
            } else if (detail instanceof ScatterPlotDetail) {
                extractScatterPlotData((ScatterPlotDetail) detail, info);
            }
            
            // Set common properties
            info.setChartType(chartType);
            info.setHasData(detail.getData() != null);
            
        } catch (Exception e) {
            Log.error("Error extracting chart data: " + e.getMessage());
        }
        
        return info;
    }
    
    /**
     * Extracts data from PieChartDetail, utilizing its specific configuration.
     */
    private void extractPieChartData(PieChartDetail detail, ChartDataInfo info) {
        Object data = detail.getData();

        info.setValueType("Integer");
        info.setDefaultValue("1");
        info.setAggregationFunction("Integer::sum");
        info.setHasLabels(true);
        info.setEntryAdditionCode("builder.addEntry(entry.getKey(), entry.getValue());");

        // Extract configuration from PieChartDetail
        List<String> configLines = new ArrayList<>();
        configLines.add("// Pie chart configuration from PieChartDetail");
        if (detail.getInnerRadius() > 0) {
            configLines.add("builder.setInnerRadius(" + detail.getInnerRadius() + ");");
        }

        info.setConfigurationLines(configLines);
        info.setHasConfiguration(true);

        // Extract actual data if available
        if (data != null) {
            info.setHasActualData(true);
            info.setDataEntryCount(1); // Simplified for now
        }
    }
    
    /**
     * Extracts data from BarChartDetail, utilizing its specific configuration.
     */
    private void extractBarChartData(BarChartDetail detail, ChartDataInfo info) {
        GemBarChartData data = detail.getData();
        
        info.setValueType("List<Integer>");
        info.setDefaultValue("Arrays.asList(1)");
        info.setAggregationFunction("(a, b) -> { a.addAll(b); return a; }");
        info.setHasLabels(true);
        info.setEntryAdditionCode("builder.addEntry(entry.getKey(), entry.getValue());");
        
        // Extract configuration from BarChartDetail
        List<String> configLines = new ArrayList<>();
        configLines.add("// Bar chart configuration from BarChartDetail");
        if (detail.isStacked()) {
            configLines.add("builder.setStacked(true);");
        }
        if (detail.getMaxValue() != 100) {
            configLines.add("builder.setMaxValue(" + detail.getMaxValue() + ");");
        }
        if (detail.getMinValue() != 0) {
            configLines.add("builder.setMinValue(" + detail.getMinValue() + ");");
        }
        
        info.setConfigurationLines(configLines);
        info.setHasConfiguration(true);
        
        // Extract actual data if available
        if (data != null && data.getEntries() != null && !data.getEntries().isEmpty()) {
            info.setHasActualData(true);
            info.setDataEntryCount(data.getEntries().size());
        }
    }
    
    /**
     * Extracts data from LineChartDetail, utilizing its specific configuration.
     */
    private void extractLineChartData(LineChartDetail detail, ChartDataInfo info) {
        GemLineChartData data = detail.getData();
        
        info.setValueType("List<Double>");
        info.setDefaultValue("Arrays.asList(1.0)");
        info.setAggregationFunction("(a, b) -> { a.addAll(b); return a; }");
        info.setHasLabels(true);
        info.setEntryAdditionCode("builder.addEntry(entry.getKey(), entry.getValue());");
        
        // Extract configuration from LineChartDetail
        List<String> configLines = new ArrayList<>();
        configLines.add("// Line chart configuration from LineChartDetail");
        if (detail.isEnableBackgroundColor()) {
            configLines.add("builder.setEnableBackgroundColor(true);");
        }
        if (detail.getMaxValue() != 100) {
            configLines.add("builder.setMaxValue(" + detail.getMaxValue() + ");");
        }
        if (detail.getMinValue() != 0) {
            configLines.add("builder.setMinValue(" + detail.getMinValue() + ");");
        }
        
        info.setConfigurationLines(configLines);
        info.setHasConfiguration(true);
        
        // Extract actual data if available
        if (data != null && data.getEntries() != null && !data.getEntries().isEmpty()) {
            info.setHasActualData(true);
            info.setDataEntryCount(data.getEntries().size());
        }
    }
    
    /**
     * Extracts data from ScatterPlotDetail, utilizing its specific configuration.
     */
    private void extractScatterPlotData(ScatterPlotDetail detail, ChartDataInfo info) {
        GemScatterPlotData data = detail.getData();
        
        info.setValueType("GemScatterPlotPoint");
        info.setDefaultValue("new GemScatterPlotPoint(0.0, 0.0)");
        info.setAggregationFunction("(a, b) -> b"); // Keep last point for scatter plots
        info.setHasLabels(false);
        info.setEntryAdditionCode("builder.addPoint(entry.getValue());");
        
        // Extract configuration from ScatterPlotDetail
        List<String> configLines = new ArrayList<>();
        configLines.add("// Scatter plot configuration from ScatterPlotDetail");
        if (detail.getXAxis() != null) {
            configLines.add("builder.setXAxis(xAxis);");
        }
        if (detail.getYAxis() != null) {
            configLines.add("builder.setYAxis(yAxis);");
        }
        
        info.setConfigurationLines(configLines);
        info.setHasConfiguration(true);
        
        // Extract actual data if available
        if (data != null && data.getPoints() != null && !data.getPoints().isEmpty()) {
            info.setHasActualData(true);
            info.setDataEntryCount(data.getPoints().size());
        }
    }
    
    /**
     * Data container class that holds extracted chart information.
     * Replaces hardcoded template generation with data-driven approach.
     */
    public static class ChartDataInfo {
        private String attributeName;
        private String valueType = "Object";
        private String defaultValue = "null";
        private String aggregationFunction = "(a, b) -> b";
        private String entryAdditionCode = "";
        private boolean hasLabels = false;
        private boolean hasConfiguration = false;
        private boolean hasActualData = false;
        private boolean hasData = false;
        private boolean hasMultipleDataSeries = false;
        private int dataEntryCount = 0;
        private ChartType chartType;
        private List<String> configurationLines = new ArrayList<>();
        
        // Getters and setters
        public String getAttributeName() { return attributeName; }
        public void setAttributeName(String attributeName) { this.attributeName = attributeName; }
        
        public String getValueType() { return valueType; }
        public void setValueType(String valueType) { this.valueType = valueType; }
        
        public String getDefaultValue() { return defaultValue; }
        public void setDefaultValue(String defaultValue) { this.defaultValue = defaultValue; }
        
        public String getAggregationFunction() { return aggregationFunction; }
        public void setAggregationFunction(String aggregationFunction) { this.aggregationFunction = aggregationFunction; }
        
        public String getEntryAdditionCode() { return entryAdditionCode; }
        public void setEntryAdditionCode(String entryAdditionCode) { this.entryAdditionCode = entryAdditionCode; }
        
        public boolean hasLabels() { return hasLabels; }
        public void setHasLabels(boolean hasLabels) { this.hasLabels = hasLabels; }
        
        public boolean hasConfiguration() { return hasConfiguration; }
        public void setHasConfiguration(boolean hasConfiguration) { this.hasConfiguration = hasConfiguration; }
        
        public boolean hasActualData() { return hasActualData; }
        public void setHasActualData(boolean hasActualData) { this.hasActualData = hasActualData; }
        
        public boolean hasData() { return hasData; }
        public void setHasData(boolean hasData) { this.hasData = hasData; }
        
        public boolean hasMultipleDataSeries() { return hasMultipleDataSeries; }
        public void setHasMultipleDataSeries(boolean hasMultipleDataSeries) { this.hasMultipleDataSeries = hasMultipleDataSeries; }
        
        public int getDataEntryCount() { return dataEntryCount; }
        public void setDataEntryCount(int dataEntryCount) { this.dataEntryCount = dataEntryCount; }
        
        public ChartType getChartType() { return chartType; }
        public void setChartType(ChartType chartType) { this.chartType = chartType; }
        
        public List<String> getConfigurationLines() { return configurationLines; }
        public void setConfigurationLines(List<String> configurationLines) { this.configurationLines = configurationLines; }
    }
}
