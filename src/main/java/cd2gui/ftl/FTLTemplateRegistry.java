package cd2gui.ftl;

import cd2gui.data.ChartType;
import de.se_rwth.commons.logging.Log;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * Registry for FTL template patterns and chart type mappings.
 * Utilizes the ChartType enum more effectively than hardcoded switch statements.
 * Provides centralized mapping between chart types and their corresponding data structures.
 * 
 * <AUTHOR> to utilize unused data classes
 */
public class FTLTemplateRegistry {
    
    private final Map<ChartType, String> returnTypeMapping;
    private final Map<ChartType, String> builderTypeMapping;
    private final Map<ChartType, String> dataTypeMapping;
    private final Map<ChartType, String> entryTypeMapping;
    
    public FTLTemplateRegistry() {
        this.returnTypeMapping = new HashMap<>();
        this.builderTypeMapping = new HashMap<>();
        this.dataTypeMapping = new HashMap<>();
        this.entryTypeMapping = new HashMap<>();
        
        initializeMappings();
    }
    
    /**
     * Initializes all chart type mappings using the ChartType enum.
     * This replaces hardcoded switch statements with data-driven configuration.
     */
    private void initializeMappings() {
        // Core chart types - these are the main ones supported
        initializeCoreChartMappings();
        
        // Extended chart types - utilizing the full ChartType enum
        initializeExtendedChartMappings();
        
        Log.info("Initialized FTL template registry with " + returnTypeMapping.size() + " chart types", "FTLTemplateRegistry");
    }
    
    /**
     * Initializes mappings for core chart types (PIE, BAR, LINE, SCATTER).
     */
    private void initializeCoreChartMappings() {
        // PIE_CHART mappings
        returnTypeMapping.put(ChartType.PIE_CHART, "GemPieChartData");
        builderTypeMapping.put(ChartType.PIE_CHART, "GemPieChartDataBuilder");
        dataTypeMapping.put(ChartType.PIE_CHART, "GemPieChartData");
        entryTypeMapping.put(ChartType.PIE_CHART, "GemPieChartEntry");
        
        // BAR_CHART mappings
        returnTypeMapping.put(ChartType.BAR_CHART, "GemBarChartData");
        builderTypeMapping.put(ChartType.BAR_CHART, "GemBarChartDataBuilder");
        dataTypeMapping.put(ChartType.BAR_CHART, "GemBarChartData");
        entryTypeMapping.put(ChartType.BAR_CHART, "GemBarChartEntry");
        
        // LINE_CHART mappings
        returnTypeMapping.put(ChartType.LINE_CHART, "GemLineChartData");
        builderTypeMapping.put(ChartType.LINE_CHART, "GemLineChartDataBuilder");
        dataTypeMapping.put(ChartType.LINE_CHART, "GemLineChartData");
        entryTypeMapping.put(ChartType.LINE_CHART, "GemLineChartEntry");
        
        // SCATTER_PLOT mappings
        returnTypeMapping.put(ChartType.SCATTER_PLOT, "GemScatterPlotData");
        builderTypeMapping.put(ChartType.SCATTER_PLOT, "GemScatterPlotDataBuilder");
        dataTypeMapping.put(ChartType.SCATTER_PLOT, "GemScatterPlotData");
        entryTypeMapping.put(ChartType.SCATTER_PLOT, "GemScatterPlotPoint");
    }
    
    /**
     * Initializes mappings for extended chart types from the ChartType enum.
     * This utilizes the full range of chart types that were previously unused.
     */
    private void initializeExtendedChartMappings() {
        // HEATMAP_CHART mappings
        returnTypeMapping.put(ChartType.HEATMAP_CHART, "GemHeatmapChartData");
        builderTypeMapping.put(ChartType.HEATMAP_CHART, "GemHeatmapChartDataBuilder");
        dataTypeMapping.put(ChartType.HEATMAP_CHART, "GemHeatmapChartData");
        entryTypeMapping.put(ChartType.HEATMAP_CHART, "GemHeatmapChartEntry");
        
        // BULLET_CHART mappings
        returnTypeMapping.put(ChartType.BULLET_CHART, "GemBulletChartData");
        builderTypeMapping.put(ChartType.BULLET_CHART, "GemBulletChartDataBuilder");
        dataTypeMapping.put(ChartType.BULLET_CHART, "GemBulletChartData");
        entryTypeMapping.put(ChartType.BULLET_CHART, "GemBulletChartEntry");
        
        // CANDLESTICK_CHART mappings
        returnTypeMapping.put(ChartType.CANDLESTICK_CHART, "GemCandlestickChartData");
        builderTypeMapping.put(ChartType.CANDLESTICK_CHART, "GemCandlestickChartDataBuilder");
        dataTypeMapping.put(ChartType.CANDLESTICK_CHART, "GemCandlestickChartData");
        entryTypeMapping.put(ChartType.CANDLESTICK_CHART, "GemCandlestickChartEntry");
        
        // GAUGE_CHART mappings
        returnTypeMapping.put(ChartType.GAUGE_CHART, "GemGaugeChartData");
        builderTypeMapping.put(ChartType.GAUGE_CHART, "GemGaugeChartDataBuilder");
        dataTypeMapping.put(ChartType.GAUGE_CHART, "GemGaugeChartData");
        entryTypeMapping.put(ChartType.GAUGE_CHART, "GemGaugeChartEntry");
        
        // RADAR_CHART mappings
        returnTypeMapping.put(ChartType.RADAR_CHART, "GemRadarChartData");
        builderTypeMapping.put(ChartType.RADAR_CHART, "GemRadarChartDataBuilder");
        dataTypeMapping.put(ChartType.RADAR_CHART, "GemRadarChartData");
        entryTypeMapping.put(ChartType.RADAR_CHART, "GemRadarChartEntry");
        
        // SUNBURST_CHART mappings
        returnTypeMapping.put(ChartType.SUNBURST_CHART, "GemSunburstChartData");
        builderTypeMapping.put(ChartType.SUNBURST_CHART, "GemSunburstChartDataBuilder");
        dataTypeMapping.put(ChartType.SUNBURST_CHART, "GemSunburstChartData");
        entryTypeMapping.put(ChartType.SUNBURST_CHART, "GemSunburstChartEntry");
        
        // TREEMAP_CHART mappings
        returnTypeMapping.put(ChartType.TREEMAP_CHART, "GemTreemapChartData");
        builderTypeMapping.put(ChartType.TREEMAP_CHART, "GemTreemapChartDataBuilder");
        dataTypeMapping.put(ChartType.TREEMAP_CHART, "GemTreemapChartData");
        entryTypeMapping.put(ChartType.TREEMAP_CHART, "GemTreemapChartEntry");
        
        // WATERFALL_CHART mappings
        returnTypeMapping.put(ChartType.WATERFALL_CHART, "GemWaterfallChartData");
        builderTypeMapping.put(ChartType.WATERFALL_CHART, "GemWaterfallChartDataBuilder");
        dataTypeMapping.put(ChartType.WATERFALL_CHART, "GemWaterfallChartData");
        entryTypeMapping.put(ChartType.WATERFALL_CHART, "GemWaterfallChartEntry");
        
        // VENN_DIAGRAM mappings
        returnTypeMapping.put(ChartType.VENN_DIAGRAM, "GemVennDiagramData");
        builderTypeMapping.put(ChartType.VENN_DIAGRAM, "GemVennDiagramDataBuilder");
        dataTypeMapping.put(ChartType.VENN_DIAGRAM, "GemVennDiagramData");
        entryTypeMapping.put(ChartType.VENN_DIAGRAM, "GemVennDiagramEntry");
    }
    
    /**
     * Gets the return type for a given chart type.
     * 
     * @param chartType The chart type
     * @return The corresponding return type, or "Object" if not found
     */
    public String getReturnType(ChartType chartType) {
        return returnTypeMapping.getOrDefault(chartType, "Object");
    }
    
    /**
     * Gets the builder type for a given chart type.
     * 
     * @param chartType The chart type
     * @return The corresponding builder type, or "Object" if not found
     */
    public String getBuilderType(ChartType chartType) {
        return builderTypeMapping.getOrDefault(chartType, "Object");
    }
    
    /**
     * Gets the data type for a given chart type.
     * 
     * @param chartType The chart type
     * @return The corresponding data type, or "Object" if not found
     */
    public String getDataType(ChartType chartType) {
        return dataTypeMapping.getOrDefault(chartType, "Object");
    }
    
    /**
     * Gets the entry type for a given chart type.
     * 
     * @param chartType The chart type
     * @return The corresponding entry type, or "Object" if not found
     */
    public String getEntryType(ChartType chartType) {
        return entryTypeMapping.getOrDefault(chartType, "Object");
    }
    
    /**
     * Checks if a chart type is supported by the registry.
     * 
     * @param chartType The chart type to check
     * @return true if supported, false otherwise
     */
    public boolean isSupported(ChartType chartType) {
        return returnTypeMapping.containsKey(chartType);
    }
    
    /**
     * Gets all supported chart types.
     * 
     * @return Set of supported chart types
     */
    public Set<ChartType> getSupportedChartTypes() {
        return returnTypeMapping.keySet();
    }
    
    /**
     * Gets the number of supported chart types.
     * 
     * @return Number of supported chart types
     */
    public int getSupportedChartTypeCount() {
        return returnTypeMapping.size();
    }
}
