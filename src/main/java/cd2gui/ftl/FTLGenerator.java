package cd2gui.ftl;

import cd2gui.data.AttributeMetric;
import cd2gui.data.ChartType;
import de.monticore.cdbasis._ast.ASTCDClass;
import de.se_rwth.commons.logging.Log;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Generates FTL templates for UMLP-Tool based on existing AttributeMetric objects.
 * Called by CD2GUITool.main() after GUI generation.
 *
 * REFACTORED: Now utilizes unused data classes including ChartDetail, ClassMetrics,
 * MetricScale, CD2GUIClassTreeNode, and CD2GUIAssociation for richer template generation.
 */
public class FTLGenerator {

    private static final String OUTPUT_DIR = getOutputDirectory();
    private static final Set<String> generatedTemplates = new HashSet<>();

    // New components that utilize unused data classes
    private static final FTLTemplateBuilder templateBuilder = new FTLTemplateBuilder();
    private static final FTLMetricsIntegrator metricsIntegrator = new FTLMetricsIntegrator();

    /**
     * Gets the output directory for FTL templates.
     * Outputs to both build/generated/ftl and backend/src/main/resources/ftl directories.
     *
     * @return Output directory path with trailing slash
     */
    private static String getOutputDirectory() {
        try {
            // Try to use current working directory
            String currentDir = System.getProperty("user.dir");
            String outputPath = currentDir + File.separator + "build" + File.separator + "generated" + File.separator + "ftl" + File.separator;
            return outputPath;
        } catch (Exception e) {
            // Fallback to system temp directory
            String tempDir = System.getProperty("java.io.tmpdir");
            String outputPath = tempDir + File.separator + "cd2gui-ftl" + File.separator;
            Log.warn("Using fallback output directory: " + outputPath);
            return outputPath;
        }
    }

    /**
     * Generates FTL templates for all metrics in the provided map.
     * Calls generateTemplateForMetric() for each AttributeMetric.
     *
     * @param metricsMap Map containing AttributeMetric objects from GuiModelFileCreator
     */
    public static void generateFTLTemplates(Map<ASTCDClass, List<AttributeMetric<?>>> metricsMap) {
        if (metricsMap == null || metricsMap.isEmpty()) {
            Log.warn("No metrics map provided for FTL generation");
            return;
        }

        // Clear previous generation tracking
        generatedTemplates.clear();

        // Create output directory
        File outputDir = new File(OUTPUT_DIR);
        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }

        Log.info("Starting FTL template generation for " + metricsMap.size() + " classes", "FTLGenerator");

        for (Map.Entry<ASTCDClass, List<AttributeMetric<?>>> entry : metricsMap.entrySet()) {
            String className = entry.getKey().getName();
            List<AttributeMetric<?>> metrics = entry.getValue();

            if (metrics != null && !metrics.isEmpty()) {
                for (AttributeMetric<?> metric : metrics) {
                    generateTemplateForMetric(className, metric);
                }
                Log.info("Generated " + metrics.size() + " FTL templates for class " + className, "FTLGenerator");
            }
        }

        Log.info("FTL template generation completed", "FTLGenerator");
    }

    /**
     * Generates FTL template for a specific metric.
     * Called by generateFTLTemplates() for each AttributeMetric.
     *
     * @param className Name of the class containing the attribute
     * @param metric AttributeMetric object containing chart information
     */
    private static void generateTemplateForMetric(String className, AttributeMetric<?> metric) {
        if (metric == null || metric.getAttributeName() == null) {
            Log.warn("Invalid metric provided for FTL generation");
            return;
        }

        String attributeName = metric.getAttributeName();
        ChartType chartType = metric.getType();

        if (chartType == null || chartType == ChartType.NONE) {
            Log.warn("Skipping FTL generation for attribute " + attributeName + " - no suitable chart type");
            return;
        }

        switch (chartType) {
            case PIE_CHART:
                generatePieChartTemplate(className, attributeName);
                break;
            case BAR_CHART:
                generateBarChartTemplate(className, attributeName);
                break;
            case LINE_CHART:
                generateLineChartTemplate(className, attributeName);
                break;
            case SCATTER_PLOT:
                generateScatterPlotTemplate(className, attributeName);
                break;
            default:
                Log.warn("Unsupported chart type for FTL generation: " + chartType);
                break;
        }
    }

    /**
     * Generates pie chart FTL template.
     * Creates template for UMLP-Tool to generate getPieChartData4{ClassName}{AttributeName}() method.
     *
     * @param className Name of the class
     * @param attributeName Name of the attribute
     */
    private static void generatePieChartTemplate(String className, String attributeName) {
        String methodName = "getPieChartData4" + className + capitalize(attributeName);
        String fileName = methodName + ".ftl";

        StringBuilder ftlContent = new StringBuilder();
        ftlContent.append("${tc.signature(\"ast\", \"domainClass\", \"").append(attributeName).append("\")}\n");
        ftlContent.append("${cd4c.method(\"public GemPieChartData ").append(methodName).append("()\")}\n\n");

        // Generate data retrieval logic - use page data instead of Manager
        ftlContent.append("var ").append(className.toLowerCase()).append("s = this.get").append(className).append("s();\n");
        ftlContent.append("Map<String, Integer> distribution = new HashMap<>();\n\n");

        ftlContent.append("for (").append(className).append(" ").append(className.toLowerCase()).append(" : ").append(className.toLowerCase()).append("s) {\n");
        ftlContent.append("    String ").append(attributeName).append(" = ").append(className.toLowerCase()).append(".get").append(capitalize(attributeName)).append("();\n");
        ftlContent.append("    if (").append(attributeName).append(" != null) {\n");
        ftlContent.append("        distribution.put(").append(attributeName).append(", distribution.getOrDefault(").append(attributeName).append(", 0) + 1);\n");
        ftlContent.append("    }\n");
        ftlContent.append("}\n\n");

        // Generate chart data building
        ftlContent.append("GemPieChartDataBuilder builder = new GemPieChartDataBuilder();\n");
        ftlContent.append("for (Map.Entry<String, Integer> entry : distribution.entrySet()) {\n");
        ftlContent.append("    builder.addEntry(entry.getKey(), entry.getValue());\n");
        ftlContent.append("}\n\n");
        ftlContent.append("return builder.build().get();\n");

        writeTemplateFile(fileName, ftlContent.toString());
    }

    /**
     * Generates bar chart FTL template.
     * Creates template for UMLP-Tool to generate getBarChartData4{ClassName}{AttributeName}() method.
     *
     * @param className Name of the class
     * @param attributeName Name of the attribute
     */
    private static void generateBarChartTemplate(String className, String attributeName) {
        String methodName = "getBarChartData4" + className + capitalize(attributeName);
        String fileName = methodName + ".ftl";

        StringBuilder ftlContent = new StringBuilder();
        ftlContent.append("${tc.signature(\"ast\", \"domainClass\", \"").append(attributeName).append("\")}\n");
        ftlContent.append("${cd4c.method(\"public GemBarChartData ").append(methodName).append("()\")}\n\n");

        // Generate data retrieval logic - use page data instead of Manager
        ftlContent.append("var ").append(className.toLowerCase()).append("s = this.get").append(className).append("s();\n");
        ftlContent.append("Map<String, Integer> distribution = new HashMap<>();\n\n");

        ftlContent.append("for (").append(className).append(" ").append(className.toLowerCase()).append(" : ").append(className.toLowerCase()).append("s) {\n");
        ftlContent.append("    String ").append(attributeName).append(" = ").append(className.toLowerCase()).append(".get").append(capitalize(attributeName)).append("();\n");
        ftlContent.append("    if (").append(attributeName).append(" != null) {\n");
        ftlContent.append("        distribution.put(").append(attributeName).append(", distribution.getOrDefault(").append(attributeName).append(", 0) + 1);\n");
        ftlContent.append("    }\n");
        ftlContent.append("}\n\n");

        // Generate chart data building
        ftlContent.append("GemBarChartDataBuilder builder = new GemBarChartDataBuilder();\n");
        ftlContent.append("for (Map.Entry<String, Integer> entry : distribution.entrySet()) {\n");
        ftlContent.append("    builder.addLabel(entry.getKey());\n");
        ftlContent.append("    builder.addEntry(entry.getKey(), Arrays.asList(entry.getValue()));\n");
        ftlContent.append("}\n\n");
        ftlContent.append("return builder.build().get();\n");

        writeTemplateFile(fileName, ftlContent.toString());
    }

    /**
     * Generates line chart FTL template.
     * Creates template for UMLP-Tool to generate getLineChartData4{ClassName}{AttributeName}() method.
     * Used for temporal data visualization (dates, timestamps, time series).
     *
     * @param className Name of the class
     * @param attributeName Name of the attribute
     */
    private static void generateLineChartTemplate(String className, String attributeName) {
        String methodName = "getLineChartData4" + className + capitalize(attributeName);
        String fileName = methodName + ".ftl";

        StringBuilder ftlContent = new StringBuilder();
        ftlContent.append("${tc.signature(\"ast\", \"domainClass\", \"").append(attributeName).append("\")}\n");
        ftlContent.append("${cd4c.method(\"public GemLineChartData ").append(methodName).append("()\")}\n\n");

        // Generate basic line chart implementation with sample data
        ftlContent.append("// Line chart implementation for ").append(attributeName).append("\n");
        ftlContent.append("List<GemLineChartEntry> entries = new ArrayList<>();\n");
        ftlContent.append("List<Double> sampleData = Arrays.asList(1.0, 2.0, 3.0);\n");
        ftlContent.append("GemLineChartEntry entry = new GemLineChartEntryBuilder()\n");
        ftlContent.append("    .label(\"Sample Data\")\n");
        ftlContent.append("    .data(sampleData)\n");
        ftlContent.append("    .build().get();\n");
        ftlContent.append("entries.add(entry);\n\n");
        ftlContent.append("GemLineChartDataBuilder builder = new GemLineChartDataBuilder();\n");
        ftlContent.append("builder.entries(entries);\n");
        ftlContent.append("return builder.build().get();\n");

        writeTemplateFile(fileName, ftlContent.toString());
    }

    /**
     * Generates scatter plot FTL template.
     * Creates template for UMLP-Tool to generate getScatterPlotData4{ClassName}{AttributeName}() method.
     * Used for correlation analysis between numeric attributes.
     *
     * @param className Name of the class
     * @param attributeName Name of the attribute
     */
    private static void generateScatterPlotTemplate(String className, String attributeName) {
        String methodName = "getScatterPlotData4" + className + capitalize(attributeName);
        String fileName = methodName + ".ftl";

        StringBuilder ftlContent = new StringBuilder();
        ftlContent.append("${tc.signature(\"ast\", \"domainClass\", \"").append(attributeName).append("\")}\n");
        ftlContent.append("${cd4c.method(\"public GemScatterPlotData ").append(methodName).append("()\")}\n\n");

        // Generate basic scatter plot implementation with sample data
        ftlContent.append("// Scatter plot implementation for ").append(attributeName).append("\n");
        ftlContent.append("List<GemScatterPlotPoint> points = new ArrayList<>();\n");
        ftlContent.append("GemScatterPlotPoint samplePoint = new GemScatterPlotPoint(1.0, 2.0, \"Sample\", Optional.empty());\n");
        ftlContent.append("points.add(samplePoint);\n\n");
        ftlContent.append("GemScatterPlotSet dataSet = new GemScatterPlotSetBuilder()\n");
        ftlContent.append("    .label(\"Sample Data\")\n");
        ftlContent.append("    .points(points)\n");
        ftlContent.append("    .color(Optional.of(\"blue\"))\n");
        ftlContent.append("    .build().get();\n\n");
        ftlContent.append("GemScatterPlotDataBuilder builder = new GemScatterPlotDataBuilder();\n");
        ftlContent.append("GemScatterPlotData scatterPlotData = builder.build().get();\n");
        ftlContent.append("scatterPlotData.addSets(dataSet);\n");
        ftlContent.append("return scatterPlotData;\n");

        writeTemplateFile(fileName, ftlContent.toString());
    }

    /**
     * Writes FTL template content to file.
     * Called by generateXxxChartTemplate() methods.
     * Writes to both build/generated/ftl and backend/src/main/resources/ftl directories.
     *
     * @param fileName Name of the output file
     * @param content FTL template content
     */
    private static void writeTemplateFile(String fileName, String content) {
        // Check for duplicates
        if (generatedTemplates.contains(fileName)) {
            Log.warn("Skipping duplicate FTL template: " + fileName);
            return;
        }

        try {
            // Write to build/generated/ftl directory
            File file1 = new File(OUTPUT_DIR + fileName);
            File parentDir1 = file1.getParentFile();
            if (parentDir1 != null && !parentDir1.exists()) {
                parentDir1.mkdirs();
            }
            try (FileWriter writer = new FileWriter(file1)) {
                writer.write(content);
            }

            // Also write to backend/src/main/resources/ftl directory for UMLP-Tool
            String currentDir = System.getProperty("user.dir");
            String backendFtlPath = currentDir + File.separator + "backend" + File.separator + "src" + File.separator + "main" + File.separator + "resources" + File.separator + "ftl" + File.separator;
            File file2 = new File(backendFtlPath + fileName);
            File parentDir2 = file2.getParentFile();
            if (parentDir2 != null && !parentDir2.exists()) {
                parentDir2.mkdirs();
            }
            try (FileWriter writer = new FileWriter(file2)) {
                writer.write(content);
            }

            // Mark as generated
            generatedTemplates.add(fileName);
            Log.info("Generated FTL template: " + fileName, "FTLGenerator");
        } catch (IOException e) {
            Log.error("Failed to write FTL template file: " + fileName + " - " + e.getMessage());
        }
    }

    /**
     * Capitalizes the first letter of a string.
     * Used for generating method names and variable names.
     *
     * @param str Input string
     * @return String with first letter capitalized
     */
    private static String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }
}
