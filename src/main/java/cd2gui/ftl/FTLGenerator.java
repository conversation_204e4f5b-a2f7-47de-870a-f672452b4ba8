package cd2gui.ftl;

import cd2gui.data.AttributeMetric;
import cd2gui.data.ChartType;
import cd2gui.data.ClassMetrics;
import de.monticore.cdbasis._ast.ASTCDClass;
import de.se_rwth.commons.logging.Log;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * REFACTORED: FTL Data Method Generator for existing template system.
 *
 * 分析发现 src/main/resources/tpl/metrics 下已有完整的FTL模板系统：
 * - metrics-gui.ftl: 主入口
 * - visualization-panel.ftl: 可视化面板
 * - chart-widget.ftl: 图表路由器
 * - charts/*.ftl: 具体图表组件
 *
 * 问题：现有模板调用数据方法，但这些方法不存在。
 * 解决：FTLGenerator 不再生成FTL文件，改为生成模板需要的数据处理方法。
 *
 * 现有模板调用的方法：
 * - processDataFor${domainClass.getName()}${attributeMetric.getAttributeName()?cap_first}()
 * - getLineChartData4${domainClass.getName()}${attributeMetric.getAttributeName()?cap_first}()
 * - barChartDetail.getData() (直接使用ChartDetail)
 */
public class FTLGenerator {

    private static final String OUTPUT_DIR = getOutputDirectory();
    private static final Set<String> generatedTemplates = new HashSet<>();

    /**
     * Gets the output directory for FTL templates.
     * Outputs to both build/generated/ftl and backend/src/main/resources/ftl directories.
     *
     * @return Output directory path with trailing slash
     */
    private static String getOutputDirectory() {
        try {
            // Try to use current working directory
            String currentDir = System.getProperty("user.dir");
            String outputPath = currentDir + File.separator + "build" + File.separator + "generated" + File.separator + "ftl" + File.separator;
            return outputPath;
        } catch (Exception e) {
            // Fallback to system temp directory
            String tempDir = System.getProperty("java.io.tmpdir");
            String outputPath = tempDir + File.separator + "cd2gui-ftl" + File.separator;
            Log.warn("Using fallback output directory: " + outputPath);
            return outputPath;
        }
    }

    /**
     * REFACTORED: 生成现有FTL模板系统需要的数据处理方法，而不是生成新的FTL文件。
     *
     * 现有模板系统 (src/main/resources/tpl/metrics) 已经完整，只需要数据方法支持：
     * - chart-widget.ftl 根据 ChartType 路由到具体图表
     * - 各图表模板调用对应的数据方法
     * - 充分利用 ChartDetail 实现类的配置和数据
     *
     * @param metricsMap Map containing AttributeMetric objects from GuiModelFileCreator
     */
    public static void generateFTLTemplates(Map<ASTCDClass, List<AttributeMetric<?>>> metricsMap) {
        if (metricsMap == null || metricsMap.isEmpty()) {
            Log.warn("No metrics map provided for data method generation");
            return;
        }

        // Clear previous generation tracking
        generatedTemplates.clear();

        // Create output directory for data methods (not FTL templates)
        File outputDir = new File(OUTPUT_DIR);
        if (!outputDir.exists()) {
            outputDir.mkdirs();
        }

        Log.info("Starting data method generation for existing FTL template system (" + metricsMap.size() + " classes)", "FTLGenerator");

        for (Map.Entry<ASTCDClass, List<AttributeMetric<?>>> entry : metricsMap.entrySet()) {
            ASTCDClass clazz = entry.getKey();
            String className = clazz.getName();
            List<AttributeMetric<?>> metrics = entry.getValue();

            if (metrics != null && !metrics.isEmpty()) {
                // 为每个度量生成对应的数据处理方法
                for (AttributeMetric<?> metric : metrics) {
                    generateDataMethodForMetric(className, metric);
                }
                Log.info("Generated " + metrics.size() + " data methods for class " + className, "FTLGenerator");
            }
        }

        Log.info("Data method generation completed for existing FTL template system", "FTLGenerator");
    }

    /**
     * REFACTORED: 为现有FTL模板系统生成数据处理方法。
     *
     * 现有模板调用的方法模式：
     * - PIE_CHART: processDataFor${domainClass.getName()}${attributeMetric.getAttributeName()?cap_first}()
     * - LINE_CHART: getLineChartData4${domainClass.getName()}${attributeMetric.getAttributeName()?cap_first}()
     * - BAR_CHART: 直接使用 barChartDetail.getData()
     * - SCATTER_PLOT: processDataFor${domainClass.getName()}${attributeMetric.getAttributeName()?cap_first}()
     *
     * @param className Name of the class containing the attribute
     * @param metric AttributeMetric object containing ChartDetail with actual data
     */
    private static void generateDataMethodForMetric(String className, AttributeMetric<?> metric) {
        if (metric == null || metric.getAttributeName() == null) {
            Log.warn("Invalid metric provided for data method generation");
            return;
        }

        String attributeName = metric.getAttributeName();
        ChartType chartType = metric.getType();

        if (chartType == null || chartType == ChartType.NONE) {
            Log.warn("Skipping data method generation for attribute " + attributeName + " - no suitable chart type");
            return;
        }

        // 根据现有模板的调用模式生成对应的数据方法
        String methodContent = generateDataMethodContent(className, attributeName, chartType, metric);

        if (methodContent != null && !methodContent.isEmpty()) {
            String methodName = getDataMethodName(className, attributeName, chartType);
            String fileName = methodName + ".java";

            // 生成Java方法文件而不是FTL模板
            StringBuilder javaContent = new StringBuilder();
            javaContent.append("// Generated data method for existing FTL template system\n");
            javaContent.append("// Template: ").append(getTemplateFileName(chartType)).append("\n");
            javaContent.append("// Chart type: ").append(chartType.getDisplayName()).append("\n");
            javaContent.append("// Utilizes ChartDetail: ").append(metric.getDetail().getClass().getSimpleName()).append("\n");
            javaContent.append("\n");
            javaContent.append(methodContent);

            writeDataMethodFile(fileName, javaContent.toString());

            Log.debug("Generated data method " + methodName + " for " + className + "." + attributeName +
                     " (" + chartType + ")", "FTLGenerator");
        } else {
            Log.warn("Failed to generate data method for " + className + "." + attributeName);
        }
    }

    /**
     * Generates pie chart FTL template.
     * Creates template for UMLP-Tool to generate getPieChartData4{ClassName}{AttributeName}() method.
     *
     * @param className Name of the class
     * @param attributeName Name of the attribute
     */
    private static void generatePieChartTemplate(String className, String attributeName) {
        String methodName = "getPieChartData4" + className + capitalize(attributeName);
        String fileName = methodName + ".ftl";

        StringBuilder ftlContent = new StringBuilder();
        ftlContent.append("${tc.signature(\"ast\", \"domainClass\", \"").append(attributeName).append("\")}\n");
        ftlContent.append("${cd4c.method(\"public GemPieChartData ").append(methodName).append("()\")}\n\n");

        // Generate data retrieval logic - use page data instead of Manager
        ftlContent.append("var ").append(className.toLowerCase()).append("s = this.get").append(className).append("s();\n");
        ftlContent.append("Map<String, Integer> distribution = new HashMap<>();\n\n");

        ftlContent.append("for (").append(className).append(" ").append(className.toLowerCase()).append(" : ").append(className.toLowerCase()).append("s) {\n");
        ftlContent.append("    String ").append(attributeName).append(" = ").append(className.toLowerCase()).append(".get").append(capitalize(attributeName)).append("();\n");
        ftlContent.append("    if (").append(attributeName).append(" != null) {\n");
        ftlContent.append("        distribution.put(").append(attributeName).append(", distribution.getOrDefault(").append(attributeName).append(", 0) + 1);\n");
        ftlContent.append("    }\n");
        ftlContent.append("}\n\n");

        // Generate chart data building
        ftlContent.append("GemPieChartDataBuilder builder = new GemPieChartDataBuilder();\n");
        ftlContent.append("for (Map.Entry<String, Integer> entry : distribution.entrySet()) {\n");
        ftlContent.append("    builder.addEntry(entry.getKey(), entry.getValue());\n");
        ftlContent.append("}\n\n");
        ftlContent.append("return builder.build().get();\n");

        writeTemplateFile(fileName, ftlContent.toString());
    }

    /**
     * 根据现有FTL模板的调用模式，生成对应的数据方法名称。
     */
    private static String getDataMethodName(String className, String attributeName, ChartType chartType) {
        switch (chartType) {
            case PIE_CHART:
            case SCATTER_PLOT:
                return "processDataFor" + className + capitalize(attributeName);
            case LINE_CHART:
                return "getLineChartData4" + className + capitalize(attributeName);
            case BAR_CHART:
                // BAR_CHART 直接使用 ChartDetail.getData()，不需要额外方法
                return "getBarChartDetail4" + className + capitalize(attributeName);
            default:
                return "getData4" + className + capitalize(attributeName);
        }
    }

    /**
     * 获取对应图表类型的模板文件名。
     */
    private static String getTemplateFileName(ChartType chartType) {
        switch (chartType) {
            case PIE_CHART: return "tpl.metrics.charts.pie-chart";
            case BAR_CHART: return "tpl.metrics.charts.bar-chart";
            case LINE_CHART: return "tpl.metrics.charts.line-chart";
            case SCATTER_PLOT: return "tpl.metrics.charts.scatter-plot";
            default: return "tpl.metrics.charts.text-display-component";
        }
    }

    /**
     * 生成数据方法的具体内容，充分利用ChartDetail实现类的数据和配置。
     */
    private static String generateDataMethodContent(String className, String attributeName, ChartType chartType, AttributeMetric<?> metric) {
        StringBuilder content = new StringBuilder();
        String methodName = getDataMethodName(className, attributeName, chartType);

        switch (chartType) {
            case PIE_CHART:
                content.append(generatePieChartDataMethod(className, attributeName, metric));
                break;
            case BAR_CHART:
                content.append(generateBarChartDataMethod(className, attributeName, metric));
                break;
            case LINE_CHART:
                content.append(generateLineChartDataMethod(className, attributeName, metric));
                break;
            case SCATTER_PLOT:
                content.append(generateScatterPlotDataMethod(className, attributeName, metric));
                break;
            default:
                content.append("// No specific data method needed for ").append(chartType);
                break;
        }

        return content.toString();
    }

    /**
     * 生成饼图数据方法，利用PieChartDetail的配置。
     */
    private static String generatePieChartDataMethod(String className, String attributeName, AttributeMetric<?> metric) {
        StringBuilder method = new StringBuilder();
        String methodName = "processDataFor" + className + capitalize(attributeName);

        method.append("public GemPieChartData ").append(methodName).append("() {\n");
        method.append("    // 利用现有的PieChartDetail数据和配置\n");

        if (metric.getDetail() instanceof PieChartDetail) {
            PieChartDetail pieDetail = (PieChartDetail) metric.getDetail();
            method.append("    // 使用PieChartDetail的配置: innerRadius = ").append(pieDetail.getInnerRadius()).append("\n");
            method.append("    return ((PieChartDetail) getAttributeMetric(\"").append(attributeName).append("\").getDetail()).getData();\n");
        } else {
            method.append("    // 生成默认饼图数据\n");
            method.append("    GemPieChartDataBuilder builder = new GemPieChartDataBuilder();\n");
            method.append("    // TODO: 实现具体的数据处理逻辑\n");
            method.append("    return builder.build().orElse(null);\n");
        }

        method.append("}\n");
        return method.toString();
    }

    /**
     * 生成柱状图数据方法，利用BarChartDetail的配置。
     */
    private static String generateBarChartDataMethod(String className, String attributeName, AttributeMetric<?> metric) {
        StringBuilder method = new StringBuilder();
        String methodName = "getBarChartDetail4" + className + capitalize(attributeName);

        method.append("public BarChartDetail ").append(methodName).append("() {\n");
        method.append("    // 直接返回配置好的BarChartDetail，供模板使用\n");
        method.append("    return (BarChartDetail) getAttributeMetric(\"").append(attributeName).append("\").getDetail();\n");
        method.append("}\n");

        return method.toString();
    }

    /**
     * Generates bar chart FTL template.
     * Creates template for UMLP-Tool to generate getBarChartData4{ClassName}{AttributeName}() method.
     *
     * @param className Name of the class
     * @param attributeName Name of the attribute
     */
    private static void generateBarChartTemplate(String className, String attributeName) {
        String methodName = "getBarChartData4" + className + capitalize(attributeName);
        String fileName = methodName + ".ftl";

        StringBuilder ftlContent = new StringBuilder();
        ftlContent.append("${tc.signature(\"ast\", \"domainClass\", \"").append(attributeName).append("\")}\n");
        ftlContent.append("${cd4c.method(\"public GemBarChartData ").append(methodName).append("()\")}\n\n");

        // Generate data retrieval logic - use page data instead of Manager
        ftlContent.append("var ").append(className.toLowerCase()).append("s = this.get").append(className).append("s();\n");
        ftlContent.append("Map<String, Integer> distribution = new HashMap<>();\n\n");

        ftlContent.append("for (").append(className).append(" ").append(className.toLowerCase()).append(" : ").append(className.toLowerCase()).append("s) {\n");
        ftlContent.append("    String ").append(attributeName).append(" = ").append(className.toLowerCase()).append(".get").append(capitalize(attributeName)).append("();\n");
        ftlContent.append("    if (").append(attributeName).append(" != null) {\n");
        ftlContent.append("        distribution.put(").append(attributeName).append(", distribution.getOrDefault(").append(attributeName).append(", 0) + 1);\n");
        ftlContent.append("    }\n");
        ftlContent.append("}\n\n");

        // Generate chart data building
        ftlContent.append("GemBarChartDataBuilder builder = new GemBarChartDataBuilder();\n");
        ftlContent.append("for (Map.Entry<String, Integer> entry : distribution.entrySet()) {\n");
        ftlContent.append("    builder.addLabel(entry.getKey());\n");
        ftlContent.append("    builder.addEntry(entry.getKey(), Arrays.asList(entry.getValue()));\n");
        ftlContent.append("}\n\n");
        ftlContent.append("return builder.build().get();\n");

        writeTemplateFile(fileName, ftlContent.toString());
    }

    /**
     * Generates line chart FTL template.
     * Creates template for UMLP-Tool to generate getLineChartData4{ClassName}{AttributeName}() method.
     * Used for temporal data visualization (dates, timestamps, time series).
     *
     * @param className Name of the class
     * @param attributeName Name of the attribute
     */
    private static void generateLineChartTemplate(String className, String attributeName) {
        String methodName = "getLineChartData4" + className + capitalize(attributeName);
        String fileName = methodName + ".ftl";

        StringBuilder ftlContent = new StringBuilder();
        ftlContent.append("${tc.signature(\"ast\", \"domainClass\", \"").append(attributeName).append("\")}\n");
        ftlContent.append("${cd4c.method(\"public GemLineChartData ").append(methodName).append("()\")}\n\n");

        // Generate basic line chart implementation with sample data
        ftlContent.append("// Line chart implementation for ").append(attributeName).append("\n");
        ftlContent.append("List<GemLineChartEntry> entries = new ArrayList<>();\n");
        ftlContent.append("List<Double> sampleData = Arrays.asList(1.0, 2.0, 3.0);\n");
        ftlContent.append("GemLineChartEntry entry = new GemLineChartEntryBuilder()\n");
        ftlContent.append("    .label(\"Sample Data\")\n");
        ftlContent.append("    .data(sampleData)\n");
        ftlContent.append("    .build().get();\n");
        ftlContent.append("entries.add(entry);\n\n");
        ftlContent.append("GemLineChartDataBuilder builder = new GemLineChartDataBuilder();\n");
        ftlContent.append("builder.entries(entries);\n");
        ftlContent.append("return builder.build().get();\n");

        writeTemplateFile(fileName, ftlContent.toString());
    }

    /**
     * Generates scatter plot FTL template.
     * Creates template for UMLP-Tool to generate getScatterPlotData4{ClassName}{AttributeName}() method.
     * Used for correlation analysis between numeric attributes.
     *
     * @param className Name of the class
     * @param attributeName Name of the attribute
     */
    private static void generateScatterPlotTemplate(String className, String attributeName) {
        String methodName = "getScatterPlotData4" + className + capitalize(attributeName);
        String fileName = methodName + ".ftl";

        StringBuilder ftlContent = new StringBuilder();
        ftlContent.append("${tc.signature(\"ast\", \"domainClass\", \"").append(attributeName).append("\")}\n");
        ftlContent.append("${cd4c.method(\"public GemScatterPlotData ").append(methodName).append("()\")}\n\n");

        // Generate basic scatter plot implementation with sample data
        ftlContent.append("// Scatter plot implementation for ").append(attributeName).append("\n");
        ftlContent.append("List<GemScatterPlotPoint> points = new ArrayList<>();\n");
        ftlContent.append("GemScatterPlotPoint samplePoint = new GemScatterPlotPoint(1.0, 2.0, \"Sample\", Optional.empty());\n");
        ftlContent.append("points.add(samplePoint);\n\n");
        ftlContent.append("GemScatterPlotSet dataSet = new GemScatterPlotSetBuilder()\n");
        ftlContent.append("    .label(\"Sample Data\")\n");
        ftlContent.append("    .points(points)\n");
        ftlContent.append("    .color(Optional.of(\"blue\"))\n");
        ftlContent.append("    .build().get();\n\n");
        ftlContent.append("GemScatterPlotDataBuilder builder = new GemScatterPlotDataBuilder();\n");
        ftlContent.append("GemScatterPlotData scatterPlotData = builder.build().get();\n");
        ftlContent.append("scatterPlotData.addSets(dataSet);\n");
        ftlContent.append("return scatterPlotData;\n");

        writeTemplateFile(fileName, ftlContent.toString());
    }

    /**
     * Writes FTL template content to file.
     * Called by generateXxxChartTemplate() methods.
     * Writes to both build/generated/ftl and backend/src/main/resources/ftl directories.
     *
     * @param fileName Name of the output file
     * @param content FTL template content
     */
    private static void writeTemplateFile(String fileName, String content) {
        // Check for duplicates
        if (generatedTemplates.contains(fileName)) {
            Log.warn("Skipping duplicate FTL template: " + fileName);
            return;
        }

        try {
            // Write to build/generated/ftl directory
            File file1 = new File(OUTPUT_DIR + fileName);
            File parentDir1 = file1.getParentFile();
            if (parentDir1 != null && !parentDir1.exists()) {
                parentDir1.mkdirs();
            }
            try (FileWriter writer = new FileWriter(file1)) {
                writer.write(content);
            }

            // Also write to backend/src/main/resources/ftl directory for UMLP-Tool
            String currentDir = System.getProperty("user.dir");
            String backendFtlPath = currentDir + File.separator + "backend" + File.separator + "src" + File.separator + "main" + File.separator + "resources" + File.separator + "ftl" + File.separator;
            File file2 = new File(backendFtlPath + fileName);
            File parentDir2 = file2.getParentFile();
            if (parentDir2 != null && !parentDir2.exists()) {
                parentDir2.mkdirs();
            }
            try (FileWriter writer = new FileWriter(file2)) {
                writer.write(content);
            }

            // Mark as generated
            generatedTemplates.add(fileName);
            Log.info("Generated FTL template: " + fileName, "FTLGenerator");
        } catch (IOException e) {
            Log.error("Failed to write FTL template file: " + fileName + " - " + e.getMessage());
        }
    }

    /**
     * Capitalizes the first letter of a string.
     * Used for generating method names and variable names.
     *
     * @param str Input string
     * @return String with first letter capitalized
     */
    private static String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }
}
