package cd2gui.ftl;

import cd2gui.data.*;
import de.se_rwth.commons.logging.Log;
import mc.fenix.charts.*;

import java.util.List;
import java.util.Optional;

/**
 * Advanced FTL template builder that utilizes rich ChartDetail data structures.
 * Replaces hardcoded template generation with dynamic content based on actual chart data.
 * 
 * <AUTHOR> to utilize unused data classes
 */
public class FTLTemplateBuilder {
    
    private final FTLDataExtractor dataExtractor;
    private final FTLTemplateRegistry templateRegistry;
    
    public FTLTemplateBuilder() {
        this.dataExtractor = new FTLDataExtractor();
        this.templateRegistry = new FTLTemplateRegistry();
    }
    
    /**
     * Builds FTL template content using rich ChartDetail data instead of hardcoded strings.
     * Utilizes the actual chart data, configuration, and metadata from ChartDetail implementations.
     * 
     * @param className Name of the class containing the attribute
     * @param metric AttributeMetric containing ChartDetail with actual data
     * @return Generated FTL template content
     */
    public String buildTemplateContent(String className, AttributeMetric<?> metric) {
        if (metric == null || metric.getDetail() == null) {
            Log.warn("Cannot build template - missing metric or detail data");
            return "";
        }
        
        String attributeName = metric.getAttributeName();
        ChartType chartType = metric.getType();
        ChartDetail<?> detail = metric.getDetail();
        
        // Extract rich data from ChartDetail
        FTLDataExtractor.ChartDataInfo dataInfo = dataExtractor.extractChartData(detail, chartType);
        
        // Build template using extracted data
        StringBuilder template = new StringBuilder();
        
        // Add signature with extracted metadata
        template.append(buildSignature(attributeName, dataInfo));
        template.append("\n");
        
        // Add method declaration with proper return type
        template.append(buildMethodDeclaration(className, attributeName, chartType));
        template.append("\n\n");
        
        // Add data processing logic based on actual chart data
        template.append(buildDataProcessingLogic(dataInfo, chartType));
        template.append("\n");
        
        // Add chart building logic using actual configuration
        template.append(buildChartBuildingLogic(dataInfo, chartType));
        template.append("\n");
        
        // Add return statement
        template.append("return builder.build().get();\n");
        
        return template.toString();
    }
    
    /**
     * Builds signature section using extracted chart metadata.
     */
    private String buildSignature(String attributeName, FTLDataExtractor.ChartDataInfo dataInfo) {
        StringBuilder signature = new StringBuilder();
        signature.append("${tc.signature(\"ast\", \"domainClass\"");
        
        // Add additional parameters based on chart data requirements
        if (dataInfo.hasLabels()) {
            signature.append(", \"").append(attributeName).append("\"");
        }
        if (dataInfo.hasMultipleDataSeries()) {
            signature.append(", \"dataSeriesCount\"");
        }
        
        signature.append(")}");
        return signature.toString();
    }
    
    /**
     * Builds method declaration with proper return type based on chart type.
     */
    private String buildMethodDeclaration(String className, String attributeName, ChartType chartType) {
        String methodName = "get" + chartType.name().replace("_", "") + "Data4" + className + capitalize(attributeName);
        String returnType = templateRegistry.getReturnType(chartType);
        
        return "${cd4c.method(\"public " + returnType + " " + methodName + "()\")}";
    }
    
    /**
     * Builds data processing logic based on actual chart data structure.
     */
    private String buildDataProcessingLogic(FTLDataExtractor.ChartDataInfo dataInfo, ChartType chartType) {
        StringBuilder logic = new StringBuilder();
        
        // Add data collection logic based on actual data structure
        if (dataInfo.hasLabels()) {
            logic.append("Map<String, ").append(dataInfo.getValueType()).append("> distribution = new HashMap<>();\n");
            logic.append("// Process attribute data from domain objects\n");
            logic.append("for (${domainClass.getName()} obj : ast.get${domainClass.getName()}s()) {\n");
            logic.append("    String key = String.valueOf(obj.get").append(capitalize(dataInfo.getAttributeName())).append("());\n");
            logic.append("    distribution.merge(key, ").append(dataInfo.getDefaultValue()).append(", ").append(dataInfo.getAggregationFunction()).append(");\n");
            logic.append("}\n\n");
        }
        
        return logic.toString();
    }
    
    /**
     * Builds chart building logic using actual chart configuration.
     */
    private String buildChartBuildingLogic(FTLDataExtractor.ChartDataInfo dataInfo, ChartType chartType) {
        StringBuilder logic = new StringBuilder();
        
        String builderType = templateRegistry.getBuilderType(chartType);
        logic.append(builderType).append(" builder = new ").append(builderType).append("();\n");
        
        // Add configuration based on actual ChartDetail settings
        if (dataInfo.hasConfiguration()) {
            for (String configLine : dataInfo.getConfigurationLines()) {
                logic.append(configLine).append("\n");
            }
        }
        
        // Add data entries based on actual data structure
        if (dataInfo.hasLabels()) {
            logic.append("for (Map.Entry<String, ").append(dataInfo.getValueType()).append("> entry : distribution.entrySet()) {\n");
            logic.append("    ").append(dataInfo.getEntryAdditionCode()).append("\n");
            logic.append("}\n");
        }
        
        return logic.toString();
    }
    
    /**
     * Capitalizes the first letter of a string.
     */
    private String capitalize(String str) {
        if (str == null || str.isEmpty()) {
            return str;
        }
        return str.substring(0, 1).toUpperCase() + str.substring(1);
    }
}
