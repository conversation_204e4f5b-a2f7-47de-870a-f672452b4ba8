package cd2gui.ftl;

import cd2gui.data.*;
import de.monticore.cdbasis._ast.ASTCDClass;
import de.se_rwth.commons.logging.Log;
import mc.fenix.charts.*;

import java.util.*;

/**
 * Example demonstrating how the refactored FTL system utilizes previously unused data classes.
 * This shows the integration of ChartDetail, ClassMetrics, MetricScale, CD2GUIClassTreeNode,
 * and CD2GUIAssociation in the new FTL generation system.
 * 
 * <AUTHOR> demonstration
 */
public class FTLIntegrationExample {
    
    /**
     * Demonstrates how the new system utilizes ChartDetail implementations
     * instead of generating hardcoded templates.
     */
    public static void demonstrateChartDetailUtilization() {
        Log.info("=== ChartDetail Utilization Demonstration ===", "FTLIntegrationExample");
        
        // Create a PieChartDetail with actual data (previously unused in FTL generation)
        PieChartDetail pieDetail = new PieChartDetail();
        pieDetail.addEntry("Category A", 25);
        pieDetail.addEntry("Category B", 35);
        pieDetail.addEntry("Category C", 40);
        pieDetail.setInnerRadius(30);
        
        // Create AttributeMetric using the rich ChartDetail
        AttributeMetric<GemPieChartData> pieMetric = new AttributeMetric<>("status", ChartType.PIE_CHART, pieDetail);
        
        // The new FTLTemplateBuilder extracts this rich data for template generation
        FTLTemplateBuilder builder = new FTLTemplateBuilder();
        String templateContent = builder.buildTemplateContent("Student", pieMetric);
        
        Log.info("Generated template using PieChartDetail data:", "FTLIntegrationExample");
        Log.info("Template includes inner radius: " + pieDetail.getInnerRadius(), "FTLIntegrationExample");
        Log.info("Template includes " + pieDetail.getData().getEntries().size() + " data entries", "FTLIntegrationExample");
        
        // Similarly for BarChartDetail
        BarChartDetail barDetail = new BarChartDetail();
        barDetail.addEntry("Q1", Arrays.asList(100, 120, 140));
        barDetail.addEntry("Q2", Arrays.asList(110, 130, 150));
        barDetail.setStacked(true);
        barDetail.setMaxValue(200);
        
        AttributeMetric<GemBarChartData> barMetric = new AttributeMetric<>("performance", ChartType.BAR_CHART, barDetail);
        String barTemplate = builder.buildTemplateContent("Employee", barMetric);
        
        Log.info("Generated bar chart template using BarChartDetail configuration:", "FTLIntegrationExample");
        Log.info("Template includes stacked: " + barDetail.isStacked(), "FTLIntegrationExample");
        Log.info("Template includes max value: " + barDetail.getMaxValue(), "FTLIntegrationExample");
    }
    
    /**
     * Demonstrates how ClassMetrics is now utilized in FTL generation.
     */
    public static void demonstrateClassMetricsUtilization() {
        Log.info("=== ClassMetrics Utilization Demonstration ===", "FTLIntegrationExample");
        
        // Create multiple AttributeMetric objects
        List<AttributeMetric<?>> metrics = new ArrayList<>();
        metrics.add(new AttributeMetric<>("name", ChartType.PIE_CHART, new PieChartDetail()));
        metrics.add(new AttributeMetric<>("age", ChartType.BAR_CHART, new BarChartDetail()));
        metrics.add(new AttributeMetric<>("score", ChartType.LINE_CHART, new LineChartDetail()));
        
        // ClassMetrics aggregates and filters visualizable metrics (previously unused in FTL)
        ClassMetrics classMetrics = new ClassMetrics(metrics);
        
        Log.info("ClassMetrics analysis:", "FTLIntegrationExample");
        Log.info("- Has visualizable attributes: " + classMetrics.hasVisualizableAttributes(), "FTLIntegrationExample");
        Log.info("- Total visualizable attributes: " + classMetrics.getTotalAttributesInt(), "FTLIntegrationExample");
        Log.info("- Unique attributes: " + classMetrics.getUniqueAttributeMetrics().size(), "FTLIntegrationExample");
        
        // This data is now included in FTL template generation for richer context
        FTLMetricsIntegrator integrator = new FTLMetricsIntegrator();
        Map<ASTCDClass, List<AttributeMetric<?>>> metricsMap = new HashMap<>();
        // metricsMap.put(someClass, metrics); // Would be populated in real usage
        
        Map<ASTCDClass, ClassMetrics> classMetricsMap = integrator.integrateClassMetrics(metricsMap);
        Log.info("Integrated ClassMetrics for " + classMetricsMap.size() + " classes", "FTLIntegrationExample");
    }
    
    /**
     * Demonstrates how MetricScale analysis is now integrated.
     */
    public static void demonstrateMetricScaleUtilization() {
        Log.info("=== MetricScale Utilization Demonstration ===", "FTLIntegrationExample");
        
        // Create metrics with different chart types (which map to different scales)
        List<AttributeMetric<?>> metrics = Arrays.asList(
            new AttributeMetric<>("category", ChartType.PIE_CHART, new PieChartDetail()),      // NOMINAL
            new AttributeMetric<>("rating", ChartType.BAR_CHART, new BarChartDetail()),       // ORDINAL
            new AttributeMetric<>("temperature", ChartType.LINE_CHART, new LineChartDetail()), // INTERVAL
            new AttributeMetric<>("weight", ChartType.SCATTER_PLOT, new ScatterPlotDetail())   // RATIO
        );
        
        // The new system analyzes MetricScale distribution (previously unused)
        FTLMetricsIntegrator integrator = new FTLMetricsIntegrator();
        FTLMetricsIntegrator.MetricScaleAnalysis analysis = integrator.analyzeMetricScales(metrics);
        
        Log.info("MetricScale analysis results:", "FTLIntegrationExample");
        Log.info("- Total metrics: " + analysis.getTotalMetrics(), "FTLIntegrationExample");
        Log.info("- NOMINAL scale count: " + analysis.getScaleCount(MetricScale.NOMINAL), "FTLIntegrationExample");
        Log.info("- ORDINAL scale count: " + analysis.getScaleCount(MetricScale.ORDINAL), "FTLIntegrationExample");
        Log.info("- INTERVAL scale count: " + analysis.getScaleCount(MetricScale.INTERVAL), "FTLIntegrationExample");
        Log.info("- RATIO scale count: " + analysis.getScaleCount(MetricScale.RATIO), "FTLIntegrationExample");
        
        // This analysis is now included in template generation for enhanced context
    }
    
    /**
     * Demonstrates how CD2GUIClassTreeNode data can be utilized.
     */
    public static void demonstrateClassTreeNodeUtilization() {
        Log.info("=== CD2GUIClassTreeNode Utilization Demonstration ===", "FTLIntegrationExample");
        
        // In a real scenario, these would be created from actual AST classes
        // Here we show how the tree structure data would be utilized
        
        Log.info("CD2GUIClassTreeNode provides:", "FTLIntegrationExample");
        Log.info("- Class hierarchy traversal (preOrder, postOrder)", "FTLIntegrationExample");
        Log.info("- Subclass relationships", "FTLIntegrationExample");
        Log.info("- Navigation context for templates", "FTLIntegrationExample");
        
        // The new FTLMetricsIntegrator.generateHierarchyContext() method
        // would extract this information for template generation
        
        FTLMetricsIntegrator integrator = new FTLMetricsIntegrator();
        // In real usage: ClassHierarchyContext context = integrator.generateHierarchyContext(clazz, treeNodes);
        Log.info("Hierarchy context would include subclass navigation and traversal data", "FTLIntegrationExample");
    }
    
    /**
     * Demonstrates the complete integration showing how all unused classes work together.
     */
    public static void demonstrateCompleteIntegration() {
        Log.info("=== Complete Integration Demonstration ===", "FTLIntegrationExample");
        
        // 1. Rich ChartDetail with actual data
        PieChartDetail pieDetail = new PieChartDetail();
        pieDetail.addEntry("Active", 60);
        pieDetail.addEntry("Inactive", 40);
        pieDetail.setInnerRadius(25);
        
        // 2. AttributeMetric using the ChartDetail
        AttributeMetric<GemPieChartData> metric = new AttributeMetric<>("status", ChartType.PIE_CHART, pieDetail);
        
        // 3. ClassMetrics for aggregation
        ClassMetrics classMetrics = new ClassMetrics(Arrays.asList(metric));
        
        // 4. MetricScale analysis
        FTLMetricsIntegrator integrator = new FTLMetricsIntegrator();
        FTLMetricsIntegrator.MetricScaleAnalysis scaleAnalysis = integrator.analyzeMetricScales(Arrays.asList(metric));
        
        // 5. Enhanced template generation using all this rich data
        FTLTemplateBuilder builder = new FTLTemplateBuilder();
        String templateContent = builder.buildTemplateContent("User", metric);
        
        Log.info("Complete integration results:", "FTLIntegrationExample");
        Log.info("- Used ChartDetail with " + pieDetail.getData().getEntries().size() + " entries", "FTLIntegrationExample");
        Log.info("- Used ClassMetrics with " + classMetrics.getTotalAttributesInt() + " visualizable attributes", "FTLIntegrationExample");
        Log.info("- Used MetricScale analysis with " + scaleAnalysis.getTotalMetrics() + " metrics", "FTLIntegrationExample");
        Log.info("- Generated enhanced template with rich metadata", "FTLIntegrationExample");
        
        // The old system would generate hardcoded templates
        // The new system generates data-driven templates using all these rich data structures
    }
    
    /**
     * Main method to run all demonstrations.
     */
    public static void main(String[] args) {
        Log.info("Starting FTL Integration Demonstration", "FTLIntegrationExample");
        
        demonstrateChartDetailUtilization();
        demonstrateClassMetricsUtilization();
        demonstrateMetricScaleUtilization();
        demonstrateClassTreeNodeUtilization();
        demonstrateCompleteIntegration();
        
        Log.info("FTL Integration Demonstration completed", "FTLIntegrationExample");
    }
}
