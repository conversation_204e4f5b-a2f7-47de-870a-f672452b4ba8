# Car Rental (UMLP Exercise)

## Build:
* Install Gradle 7.6.X

## Use
### Local
The project is split between the backend and frontend.
To run both backend and frontend, run `gradle runAll`.
To run the backend locally on your computer, run `gradle backend:run`.

To run the frontend locally, run `gradle frontend:runApp`.
You can now view your **website** at [localhost:4200/carrental/CD2GUIDashboard](http://localhost:4200/carrental/CD2GUIDashboard)

The CD2GUI Dashboard shows all defined classes,
 and switching to the overview of a class shows the available data.

### Server
To run the project **online**, edit the files in the remote Gitlab repository of your group.
I.e., either edit it using the online editor provided by Gitlab or clone this repository, change the files locally (probably testing it out locally, see above) and push it to Gitlab.
**You must always work on the master branch! Don't use other branches, here!**

The latest successful pipeline of the `master` branch will automatically be hosted at `https://studentprojects.se-rwth.de/sle24/<id>/`, where `id` is the id of this Gitlab project.
It is secured via the username `umlp` and password `UECM3PG78TgTjLxsSCcE`, so that only students of the SLE Lecture can access it.
Do not share these credentials with third parties.
Please note that the pipeline takes some time. You can see [the state of the latest pipeline jobs](https://git.rwth-aachen.de/monticore/umlp-anwendungen/car-rental/-/jobs).

Note: All data in the database will be deleted when the result of a pipeline is hosted or the corresponding server is restarted.
Do not rely on the database persistence.
