# (c) https://github.com/MontiCore/monticore
image: registry.git.rwth-aachen.de/monticore/container-registry/gradle:7-native-image

variables:
  GRADLE_OPTS: "-Dmaven.repo.local=$CI_PROJECT_DIR/.m2"

before_script:
  - export GRADLE_USER_HOME=`pwd`/.gradle

stages:
  - build
  - deploy

build_incl_containers:
  stage: build
  script:
    - "rm frontend/jsweet/jsweet_extension/umlp/jsweet/extension/domainAdapters/CarrentalImportAdapter.class || echo Skip deleting adapter"
    - "gradle build --refresh-dependencies -P mavenPassword=$pass -P mavenUser=$user -P buildNativeExecutable=true $GRADLE_OPTS $ARGS1"
    # Backend Container
    - "gradle :backend:jib -P mavenPassword=$pass -P mavenUser=$user -P buildNativeExecutable=true $ARGS3 \
        -Djib.to.image=$CI_REGISTRY_IMAGE/backend:latest \
        -Djib.to.auth.username=$CI_REGISTRY_USER  \
        -Djib.to.auth.password=$CI_REGISTRY_PASSWORD "
    # Frontend Container
    - "gradle :frontend:deployImageCI -P mavenPassword=$pass -P mavenUser=$user -P buildNativeExecutable=true $ARGS3"

  artifacts:
    reports:
      junit: "**/test/**/TEST-*.xml"
    paths:
      - frontend/build/frontendGen/dist/**
      - frontend/build/frontendGen/Dockerfile
      - frontend/build/frontendGen/Caddyfile
      - frontend/build/frontendGen/.dockerignore
  only:
    refs:
      - master
      - main
      - dev

build_exl_containers:
  stage: build
  script:
    - "rm frontend/jsweet/jsweet_extension/umlp/jsweet/extension/domainAdapters/CarrentalImportAdapter.class || echo Skip deleting adapter"
    - "gradle build --refresh-dependencies -P mavenPassword=$pass -P mavenUser=$user -P buildNativeExecutable=true $GRADLE_OPTS $ARGS1"

  artifacts:
    reports:
      # Lets gitlab render infos about failed tests
      junit: "**/test/**/TEST-*.xml"
  except:
    refs:
      - master
      - main
      - dev
