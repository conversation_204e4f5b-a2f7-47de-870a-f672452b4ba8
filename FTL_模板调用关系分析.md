# CD2GUI FTL 模板调用关系分析

## 概述

通过分析 `src/main/resources/tpl/metrics` 下的 FTL 文件，发现已经有一套完整的图表可视化模板系统。现有的 `FTLGenerator.java` 生成独立的 FTL 文件的方式是冗余的，应该利用这套现有的模板系统。

## 现有模板结构

### 1. 主入口模板

#### metrics-gui.ftl
- **作用**：独立的度量页面入口
- **签名**：`${tc.signature("domainClass", "name", "domainPackage", "classMetrics")}`
- **调用**：
  - `imports-metrics.ftl` - 导入所需组件
  - `visualization-panel.ftl` - 主要可视化面板

#### overview-gui.ftl
- **作用**：概览页面，集成度量可视化
- **签名**：`${tc.signature("domainClass", "name", "domainPackage", "attributes", "subclasses", "classMetrics")}`
- **集成度量**：通过 `classMetrics` 参数集成度量可视化面板

### 2. 核心组件模板

#### imports-metrics.ftl
- **作用**：导入所有需要的 mc.fenix 组件
- **包含**：
  - 基础组件：`GemCard`, `GemRow`, `GemColumn`, `GemGrid`
  - 文本组件：`GemText`, `GemButton`
  - 导航组件：`GemNavItem`
  - 表格组件：`GemTable`, `GemTableHeader`, `GemTableRow`
  - 图表组件：所有图表类型的完整导入

#### visualization-panel.ftl
- **作用**：度量可视化的主面板
- **功能**：
  - 统计摘要卡片
  - 可视化网格布局
  - 无可视化数据时的提示
- **数据驱动**：基于 `classMetrics.hasVisualizableAttributes()` 条件渲染

#### chart-widget.ftl
- **作用**：图表组件路由器
- **功能**：
  - 根据 `attributeMetric.getType()` 路由到具体图表
  - 支持 PIE_CHART, BAR_CHART, LINE_CHART, SCATTER_PLOT
  - 默认使用 `text-display-component.ftl`
- **签名**：`${tc.signature("domainClass", "name", "attributeMetric")}`

### 3. 具体图表模板

#### pie-chart.ftl
```ftl
@GemPieChart(
    data = processDataFor${domainClass.getName()}${attributeMetric.getAttributeName()?cap_first}(),
    innerRadius = 50
)
```

#### bar-chart.ftl
```ftl
@GemBarChart(
    data = barChartDetail.getData(),
    stacked = barChartDetail.isStacked(),
    maxValue = barChartDetail.getMaxValue(),
    minValue = barChartDetail.getMinValue()
)
```
- **特点**：直接使用 `attributeMetric.getDetail()` 获取 `BarChartDetail` 配置

#### line-chart.ftl
```ftl
@GemLineChart(
    data = getLineChartData4${domainClass.getName()}${attributeMetric.getAttributeName()?cap_first}()
)
```

#### scatter-plot.ftl
```ftl
@GemScatterPlot(
    data = processDataFor${domainClass.getName()}${attributeMetric.getAttributeName()?cap_first}()
)
```

#### text-display-component.ftl
- **作用**：不适合图表可视化的属性的文本显示
- **功能**：
  - 显示属性名称
  - 显示图表类型
  - 显示可视化状态

#### enhanced-table-component.ftl
- **作用**：表格形式显示属性信息
- **使用**：`GemTable` 组件展示属性详情

## 数据流分析

### 1. 数据传递链
```
GuiModelFileCreator.createMetricsPage()
  ↓ (classMetrics)
metrics-gui.ftl / overview-gui.ftl
  ↓ (classMetrics)
visualization-panel.ftl
  ↓ (attributeMetric)
chart-widget.ftl
  ↓ (attributeMetric)
具体图表模板
```

### 2. 关键数据对象

#### ClassMetrics
- `hasVisualizableAttributes()` - 判断是否有可视化属性
- `getTotalAttributesInt()` - 获取总属性数
- `getUniqueAttributeMetrics()` - 获取唯一属性度量列表

#### AttributeMetric
- `getAttributeName()` - 属性名称
- `getType()` - 图表类型（ChartType 枚举）
- `isVisualizableAttribute()` - 是否可视化
- `getDetail()` - 获取 ChartDetail 实现

#### ChartDetail 实现类
- `PieChartDetail`: `getData()`, `getInnerRadius()`
- `BarChartDetail`: `getData()`, `isStacked()`, `getMaxValue()`, `getMinValue()`
- `LineChartDetail`: `getData()`
- `ScatterPlotDetail`: `getData()`

## 问题分析

### 1. 当前 FTLGenerator.java 的问题
- **冗余生成**：生成独立的 FTL 文件，而现有模板已经完整
- **硬编码**：生成的模板内容是硬编码的，不利用 ChartDetail 的丰富配置
- **重复工作**：重新实现了已有模板的功能

### 2. 数据方法调用不一致
- `pie-chart.ftl` 调用：`processDataFor${domainClass.getName()}${attributeMetric.getAttributeName()?cap_first}()`
- `line-chart.ftl` 调用：`getLineChartData4${domainClass.getName()}${attributeMetric.getAttributeName()?cap_first}()`
- `bar-chart.ftl` 直接使用：`barChartDetail.getData()`

### 3. ChartDetail 利用不充分
- 只有 `bar-chart.ftl` 充分利用了 `ChartDetail` 的配置
- 其他图表模板没有使用 `ChartDetail` 的丰富数据

## 重构建议

### 1. 重新定位 FTLGenerator.java
- **不再生成 FTL 文件**
- **改为数据方法生成器**：生成模板中调用的数据处理方法
- **利用现有模板系统**

### 2. 统一数据方法调用
建议统一所有图表的数据方法调用方式：
```ftl
@GemPieChart(
    data = ${attributeMetric.getDetail().getData()},
    innerRadius = ${attributeMetric.getDetail().getInnerRadius()}
)
```

### 3. 充分利用 ChartDetail
修改现有图表模板，充分利用 ChartDetail 的配置：
- `pie-chart.ftl` 使用 `PieChartDetail.getInnerRadius()`
- `line-chart.ftl` 使用 `LineChartDetail` 的配置
- `scatter-plot.ftl` 使用 `ScatterPlotDetail` 的轴配置

### 4. 数据方法生成策略
FTLGenerator 应该生成 Java 方法而不是 FTL 模板：
```java
// 生成这样的方法
public GemPieChartData processDataForStudentStatus() {
    // 使用 PieChartDetail 中的实际数据
}
```

## 结论

现有的 FTL 模板系统已经很完整，问题在于：
1. **FTLGenerator.java 的定位错误** - 应该生成数据方法而不是模板
2. **ChartDetail 数据未充分利用** - 模板应该直接使用 ChartDetail 的配置
3. **数据流不一致** - 需要统一数据方法调用方式

重构的核心是让 FTLGenerator 专注于数据处理方法的生成，而图表渲染完全依赖现有的优秀模板系统。
